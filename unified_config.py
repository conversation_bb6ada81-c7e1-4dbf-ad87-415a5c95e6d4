# -*- coding: utf-8 -*-
"""
统一配置管理模块
整合所有配置项，避免冲突和重复
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import yaml

@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 5432
    user: str = "postgres"
    password: str = "password"
    dbname: str = "product_knowledge_db"
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class APIConfig:
    """API配置"""
    base_url: str = "https://zkmall.zktecoip.com"
    username: str = "18929343717"
    password: str = "Zk@123456"
    download_dir: str = "./downloads"
    timeout: int = 30
    max_retries: int = 3

@dataclass
class SiliconFlowConfig:
    """硅基流动配置"""
    api_key: str = "sk-kmaipghbqavpzfnhpuuybpgrimcroynvsqlfkbnhcjcdulxj"
    api_base_url: str = "https://api.siliconflow.cn/v1"
    models: Dict[str, str] = None
    
    def __post_init__(self):
        if self.models is None:
            self.models = {
                "vision": "Qwen/Qwen2-VL-72B-Instruct",
                "text": "Qwen/Qwen2.5-72B-Instruct", 
                "code": "deepseek-ai/DeepSeek-Coder-V2-Instruct",
                "lightweight": "Qwen/Qwen2.5-7B-Instruct"
            }

@dataclass
class FastGPTConfig:
    """FastGPT配置"""
    api_url: str = "https://api.fastgpt.in/api"
    api_key: str = ""
    dataset_id: str = ""
    timeout: int = 30
    max_retries: int = 3

@dataclass
class FilePathConfig:
    """文件路径配置"""
    allcollections: str = "./allcollections.json"
    product_structure: str = "./国内产品结构细化表.json"
    local_files_base: str = "E:/18.AI客服知识库/18.AI客服知识库"
    logs_dir: str = "./logs"
    downloads_dir: str = "./downloads"
    uploads_dir: str = "./uploads"
    temp_dir: str = "./temp"
    results_dir: str = "./results"

@dataclass
class ProcessConfig:
    """处理配置"""
    batch_size: int = 100
    max_retries: int = 3
    timeout: int = 30
    enable_local_scan: bool = True
    enable_yunshang_api: bool = True
    enable_legacy_import: bool = True
    max_concurrent_requests: int = 5
    cache_enabled: bool = True
    cache_ttl: int = 3600

@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "./logs/unified_app.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5

class UnifiedConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_file: str = "unified_config.yaml"):
        self.config_file = Path(config_file)
        self.project_root = Path(__file__).parent
        
        # 初始化配置
        self.database = DatabaseConfig()
        self.api = APIConfig()
        self.siliconflow = SiliconFlowConfig()
        self.fastgpt = FastGPTConfig()
        self.file_paths = FilePathConfig()
        self.process = ProcessConfig()
        self.log = LogConfig()
        
        # 加载配置
        self.load_config()
        
        # 设置日志
        self.setup_logging()
    
    def load_config(self):
        """加载配置文件"""
        try:
            # 尝试加载YAML配置
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                    self._update_from_dict(config_data)
                    logging.info(f"已加载配置文件: {self.config_file}")
            
            # 尝试加载环境变量
            self._load_from_env()
            
            # 尝试加载旧配置文件兼容性
            self._load_legacy_config()
            
        except Exception as e:
            logging.warning(f"加载配置失败: {e}")
    
    def _update_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        if 'database' in config_data:
            for key, value in config_data['database'].items():
                if hasattr(self.database, key):
                    setattr(self.database, key, value)
        
        if 'api' in config_data:
            for key, value in config_data['api'].items():
                if hasattr(self.api, key):
                    setattr(self.api, key, value)
        
        if 'siliconflow' in config_data:
            for key, value in config_data['siliconflow'].items():
                if hasattr(self.siliconflow, key):
                    setattr(self.siliconflow, key, value)
        
        if 'fastgpt' in config_data:
            for key, value in config_data['fastgpt'].items():
                if hasattr(self.fastgpt, key):
                    setattr(self.fastgpt, key, value)
        
        if 'file_paths' in config_data:
            for key, value in config_data['file_paths'].items():
                if hasattr(self.file_paths, key):
                    setattr(self.file_paths, key, value)
        
        if 'process' in config_data:
            for key, value in config_data['process'].items():
                if hasattr(self.process, key):
                    setattr(self.process, key, value)
        
        if 'log' in config_data:
            for key, value in config_data['log'].items():
                if hasattr(self.log, key):
                    setattr(self.log, key, value)
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 数据库配置
        if os.getenv('DB_HOST'):
            self.database.host = os.getenv('DB_HOST')
        if os.getenv('DB_PORT'):
            self.database.port = int(os.getenv('DB_PORT'))
        if os.getenv('DB_USER'):
            self.database.user = os.getenv('DB_USER')
        if os.getenv('DB_PASSWORD'):
            self.database.password = os.getenv('DB_PASSWORD')
        if os.getenv('DB_NAME'):
            self.database.dbname = os.getenv('DB_NAME')
        
        # API配置
        if os.getenv('SILICONFLOW_API_KEY'):
            self.siliconflow.api_key = os.getenv('SILICONFLOW_API_KEY')
        if os.getenv('FASTGPT_API_KEY'):
            self.fastgpt.api_key = os.getenv('FASTGPT_API_KEY')
        if os.getenv('FASTGPT_DATASET_ID'):
            self.fastgpt.dataset_id = os.getenv('FASTGPT_DATASET_ID')
    
    def _load_legacy_config(self):
        """加载旧配置文件以保持兼容性"""
        try:
            # 尝试加载config.py
            legacy_config_file = self.project_root / "config.py"
            if legacy_config_file.exists():
                import importlib.util
                spec = importlib.util.spec_from_file_location("legacy_config", legacy_config_file)
                legacy_config = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(legacy_config)
                
                # 更新配置
                if hasattr(legacy_config, 'DATABASE_CONFIG'):
                    db_config = legacy_config.DATABASE_CONFIG
                    self.database.host = db_config.get('host', self.database.host)
                    self.database.port = db_config.get('port', self.database.port)
                    self.database.user = db_config.get('user', self.database.user)
                    self.database.password = db_config.get('password', self.database.password)
                    self.database.dbname = db_config.get('dbname', self.database.dbname)
                
                if hasattr(legacy_config, 'API_CONFIG'):
                    api_config = legacy_config.API_CONFIG
                    self.api.base_url = api_config.get('base_url', self.api.base_url)
                    self.api.username = api_config.get('username', self.api.username)
                    self.api.password = api_config.get('password', self.api.password)
                
                logging.info("已加载旧配置文件兼容性设置")
        except Exception as e:
            logging.warning(f"加载旧配置文件失败: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            config_data = {
                'database': asdict(self.database),
                'api': asdict(self.api),
                'siliconflow': asdict(self.siliconflow),
                'fastgpt': asdict(self.fastgpt),
                'file_paths': asdict(self.file_paths),
                'process': asdict(self.process),
                'log': asdict(self.log)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logging.info(f"配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            logging.error(f"保存配置失败: {e}")
            return False
    
    def setup_logging(self):
        """设置日志"""
        try:
            # 创建日志目录
            log_dir = Path(self.log.file).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 配置日志
            from logging.handlers import RotatingFileHandler
            
            # 创建格式器
            formatter = logging.Formatter(self.log.format)
            
            # 文件处理器
            file_handler = RotatingFileHandler(
                self.log.file,
                maxBytes=self.log.max_file_size,
                backupCount=self.log.backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            
            # 配置根日志器
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, self.log.level))
            
            # 清除现有处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 添加新处理器
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            
        except Exception as e:
            print(f"设置日志失败: {e}")
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.file_paths.logs_dir,
            self.file_paths.downloads_dir,
            self.file_paths.uploads_dir,
            self.file_paths.temp_dir,
            self.file_paths.results_dir
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        logging.info("目录结构创建完成")
    
    def validate_config(self) -> Dict[str, bool]:
        """验证配置"""
        validation_results = {}
        
        # 验证数据库配置
        validation_results['database'] = bool(
            self.database.host and 
            self.database.user and 
            self.database.password and 
            self.database.dbname
        )
        
        # 验证API配置
        validation_results['siliconflow'] = bool(self.siliconflow.api_key)
        validation_results['fastgpt'] = bool(
            self.fastgpt.api_key and self.fastgpt.dataset_id
        )
        
        # 验证文件路径
        validation_results['file_paths'] = True  # 基本路径总是有效的
        
        return validation_results
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置字典"""
        return self.database.to_dict()
    
    def get_api_headers(self, service: str) -> Dict[str, str]:
        """获取API请求头"""
        if service == 'siliconflow':
            return {
                'Authorization': f'Bearer {self.siliconflow.api_key}',
                'Content-Type': 'application/json'
            }
        elif service == 'fastgpt':
            return {
                'Authorization': f'Bearer {self.fastgpt.api_key}',
                'Content-Type': 'application/json'
            }
        else:
            return {'Content-Type': 'application/json'}

# 全局配置实例
config_manager = UnifiedConfigManager()

# 向后兼容的配置导出
DATABASE_CONFIG = config_manager.get_database_config()
API_CONFIG = asdict(config_manager.api)
FILE_PATHS = asdict(config_manager.file_paths)
LOG_CONFIG = asdict(config_manager.log)
PROCESS_CONFIG = asdict(config_manager.process)
