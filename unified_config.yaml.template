# 产品知识库数据处理系统 - 统一配置文件模板
# 复制此文件为 unified_config.yaml 并修改相应配置

# 数据库配置
database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "your_password_here"
  dbname: "product_knowledge_db"

# API服务配置
api:
  base_url: "https://zkmall.zktecoip.com"
  username: "your_username_here"
  password: "your_password_here"
  download_dir: "./downloads"
  timeout: 30
  max_retries: 3

# 硅基流动配置
siliconflow:
  api_key: "your_siliconflow_api_key_here"
  api_base_url: "https://api.siliconflow.cn/v1"
  models:
    vision: "Qwen/Qwen2-VL-72B-Instruct"
    text: "Qwen/Qwen2.5-72B-Instruct"
    code: "deepseek-ai/DeepSeek-Coder-V2-Instruct"
    lightweight: "Qwen/Qwen2.5-7B-Instruct"

# FastGPT配置
fastgpt:
  api_url: "https://api.fastgpt.in/api"
  api_key: "your_fastgpt_api_key_here"
  dataset_id: "your_dataset_id_here"
  timeout: 30
  max_retries: 3

# 文件路径配置
file_paths:
  allcollections: "./allcollections.json"
  product_structure: "./国内产品结构细化表.json"
  local_files_base: "E:/18.AI客服知识库/18.AI客服知识库"
  logs_dir: "./logs"
  downloads_dir: "./downloads"
  uploads_dir: "./uploads"
  temp_dir: "./temp"
  results_dir: "./results"

# 处理配置
process:
  batch_size: 100
  max_retries: 3
  timeout: 30
  enable_local_scan: true
  enable_yunshang_api: true
  enable_legacy_import: true
  max_concurrent_requests: 5
  cache_enabled: true
  cache_ttl: 3600

# 日志配置
log:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "./logs/unified_app.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5

# 环境变量说明
# 以下配置项也可以通过环境变量设置：
# DB_HOST - 数据库主机
# DB_PORT - 数据库端口
# DB_USER - 数据库用户
# DB_PASSWORD - 数据库密码
# DB_NAME - 数据库名称
# SILICONFLOW_API_KEY - 硅基流动API密钥
# FASTGPT_API_KEY - FastGPT API密钥
# FASTGPT_DATASET_ID - FastGPT数据集ID
